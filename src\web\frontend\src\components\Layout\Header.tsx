import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import {
  Bars3Icon,
  MagnifyingGlassIcon,
  BellIcon,
  UserCircleIcon,
  SunIcon,
  MoonIcon,
  ComputerDesktopIcon
} from '@heroicons/react/24/outline';
import { Menu, Transition, Switch } from '@headlessui/react';
import { useTheme } from '../../hooks/useTheme';
import { clsx } from 'clsx';

interface HeaderProps {
  onMenuClick: () => void;
}

const Header: React.FC<HeaderProps> = ({ onMenuClick }) => {
  const { theme, setTheme, resolvedTheme } = useTheme();
  const [searchFocused, setSearchFocused] = useState(false);

  const themeOptions = [
    { value: 'light', label: 'Light', icon: SunIcon },
    { value: 'dark', label: 'Dark', icon: MoonIcon },
    { value: 'system', label: 'System', icon: ComputerDesktopIcon },
  ] as const;

  return (
    <div className="sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white/95 backdrop-blur-sm px-4 shadow-sm dark:border-gray-700 dark:bg-gray-800/95 sm:gap-x-6 sm:px-6 lg:px-8 transition-all duration-200">
      {/* Mobile menu button */}
      <button
        type="button"
        className="-m-2.5 p-2.5 text-gray-700 dark:text-gray-300 lg:hidden hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200"
        onClick={onMenuClick}
        aria-label="Open sidebar"
      >
        <Bars3Icon className="h-6 w-6" aria-hidden="true" />
      </button>

      {/* Separator */}
      <div className="h-6 w-px bg-gray-200 dark:bg-gray-700 lg:hidden" aria-hidden="true" />

      <div className="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
        {/* Search - Made smaller and more polished */}
        <form className="relative flex max-w-md" action="#" method="GET">
          <label htmlFor="search-field" className="sr-only">
            Search templates and examples
          </label>
          <MagnifyingGlassIcon
            className={clsx(
              "pointer-events-none absolute inset-y-0 left-0 h-full w-5 transition-colors duration-200",
              searchFocused ? "text-blue-500" : "text-gray-400"
            )}
            aria-hidden="true"
          />
          <input
            id="search-field"
            className={clsx(
              "block h-full w-full border-0 py-0 pl-8 pr-4 text-gray-900 placeholder:text-gray-400 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:bg-gray-800 dark:text-gray-100 dark:placeholder:text-gray-500 dark:focus:ring-blue-400 sm:text-sm rounded-lg transition-all duration-200",
              searchFocused ? "bg-white dark:bg-gray-700 shadow-md" : "bg-gray-50 dark:bg-gray-800"
            )}
            placeholder="Search..."
            type="search"
            name="search"
            onFocus={() => setSearchFocused(true)}
            onBlur={() => setSearchFocused(false)}
          />
        </form>

        {/* App title for better branding */}
        <div className="hidden md:flex items-center">
          <h1 className="text-lg font-semibold text-gray-900 dark:text-white">
            ArchitekAI
          </h1>
        </div>

        <div className="flex items-center gap-x-3 lg:gap-x-4">
          {/* Dark/Light Mode Toggle Switch */}
          <div className="flex items-center gap-2">
            <SunIcon className="h-4 w-4 text-gray-400" />
            <Switch
              checked={resolvedTheme === 'dark'}
              onChange={(checked) => setTheme(checked ? 'dark' : 'light')}
              className={clsx(
                resolvedTheme === 'dark' ? 'bg-blue-600' : 'bg-gray-200',
                'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-600 focus:ring-offset-2'
              )}
            >
              <span className="sr-only">Toggle dark mode</span>
              <span
                aria-hidden="true"
                className={clsx(
                  resolvedTheme === 'dark' ? 'translate-x-5' : 'translate-x-0',
                  'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'
                )}
              />
            </Switch>
            <MoonIcon className="h-4 w-4 text-gray-400" />
          </div>

          {/* Advanced Theme selector (dropdown) */}
          <Menu as="div" className="relative">
            <Menu.Button className="-m-1.5 flex items-center p-1.5 text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-all duration-200">
              <span className="sr-only">Advanced theme options</span>
              {theme === 'light' && <SunIcon className="h-5 w-5" aria-hidden="true" />}
              {theme === 'dark' && <MoonIcon className="h-5 w-5" aria-hidden="true" />}
              {theme === 'system' && <ComputerDesktopIcon className="h-5 w-5" aria-hidden="true" />}
            </Menu.Button>
            <Transition
              as={React.Fragment}
              enter="transition ease-out duration-200"
              enterFrom="transform opacity-0 scale-95"
              enterTo="transform opacity-100 scale-100"
              leave="transition ease-in duration-150"
              leaveFrom="transform opacity-100 scale-100"
              leaveTo="transform opacity-0 scale-95"
            >
              <Menu.Items className="absolute right-0 z-10 mt-2.5 w-36 origin-top-right rounded-lg bg-white py-2 shadow-xl ring-1 ring-gray-900/10 focus:outline-none dark:bg-gray-800 dark:ring-gray-700 border border-gray-200 dark:border-gray-600">
                {themeOptions.map((option) => (
                  <Menu.Item key={option.value}>
                    {({ active }) => (
                      <button
                        onClick={() => setTheme(option.value)}
                        className={clsx(
                          active ? 'bg-gray-50 dark:bg-gray-700' : '',
                          theme === option.value ? 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20' : 'text-gray-700 dark:text-gray-300',
                          'flex w-full items-center px-3 py-2 text-sm font-medium transition-colors duration-150 hover:bg-gray-50 dark:hover:bg-gray-700'
                        )}
                      >
                        <option.icon className="mr-3 h-4 w-4" aria-hidden="true" />
                        {option.label}
                      </button>
                    )}
                  </Menu.Item>
                ))}
              </Menu.Items>
            </Transition>
          </Menu>

          {/* Notifications */}
          <button
            type="button"
            className="-m-2.5 p-2.5 text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-all duration-200 relative"
            aria-label="View notifications"
          >
            <BellIcon className="h-5 w-5" aria-hidden="true" />
            {/* Notification badge */}
            <span className="absolute -top-1 -right-1 h-2 w-2 bg-red-500 rounded-full"></span>
          </button>

          {/* Separator */}
          <div className="hidden lg:block lg:h-6 lg:w-px lg:bg-gray-200 dark:lg:bg-gray-700" aria-hidden="true" />

          {/* Profile dropdown */}
          <Menu as="div" className="relative">
            <Menu.Button className="-m-1.5 flex items-center p-1.5 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-all duration-200">
              <span className="sr-only">Open user menu</span>
              <UserCircleIcon className="h-8 w-8 text-gray-400 hover:text-gray-500 dark:hover:text-gray-300" aria-hidden="true" />
              <span className="hidden lg:flex lg:items-center">
                <span className="ml-3 text-sm font-semibold leading-6 text-gray-900 dark:text-gray-100">
                  User
                </span>
              </span>
            </Menu.Button>
            <Transition
              as={React.Fragment}
              enter="transition ease-out duration-200"
              enterFrom="transform opacity-0 scale-95"
              enterTo="transform opacity-100 scale-100"
              leave="transition ease-in duration-150"
              leaveFrom="transform opacity-100 scale-100"
              leaveTo="transform opacity-0 scale-95"
            >
              <Menu.Items className="absolute right-0 z-10 mt-2.5 w-40 origin-top-right rounded-lg bg-white py-2 shadow-xl ring-1 ring-gray-900/10 focus:outline-none dark:bg-gray-800 dark:ring-gray-700 border border-gray-200 dark:border-gray-600">
                <Menu.Item>
                  {({ active }) => (
                    <Link
                      to="/settings"
                      className={clsx(
                        active ? 'bg-gray-50 dark:bg-gray-700' : '',
                        'block px-4 py-2 text-sm font-medium leading-6 text-gray-700 dark:text-gray-300 transition-colors duration-150 hover:bg-gray-50 dark:hover:bg-gray-700'
                      )}
                    >
                      Settings
                    </Link>
                  )}
                </Menu.Item>
                <div className="border-t border-gray-100 dark:border-gray-600 my-1"></div>
                <Menu.Item>
                  {({ active }) => (
                    <a
                      href="#"
                      className={clsx(
                        active ? 'bg-gray-50 dark:bg-gray-700' : '',
                        'block px-4 py-2 text-sm font-medium leading-6 text-gray-700 dark:text-gray-300 transition-colors duration-150 hover:bg-gray-50 dark:hover:bg-gray-700'
                      )}
                    >
                      Sign out
                    </a>
                  )}
                </Menu.Item>
              </Menu.Items>
            </Transition>
          </Menu>
        </div>
      </div>
    </div>
  );
};

export default Header;
