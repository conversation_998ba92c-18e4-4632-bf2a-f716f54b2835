import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { MermaidPreview } from '../components/MermaidPreview';
import { ExportButton } from '../components/ExportButton';
import { FileUpload } from '../components/FileUpload';
import { DiagramGallery } from '../components/DiagramGallery';
import { useTheme } from '../hooks/useTheme';

const GeneratePage: React.FC = () => {
  const [searchParams] = useSearchParams();
  const { resolvedTheme } = useTheme();

  const [description, setDescription] = useState('');
  const [format, setFormat] = useState('mermaid');
  const [template, setTemplate] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [result, setResult] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'generate' | 'upload' | 'gallery'>('generate');
  const [diagramTheme, setDiagramTheme] = useState<'default' | 'dark' | 'forest' | 'neutral'>('default');

  // Initialize from URL parameters
  useEffect(() => {
    const templateParam = searchParams.get('template');
    const descriptionParam = searchParams.get('description');

    if (templateParam) {
      setTemplate(templateParam);
    }

    if (descriptionParam) {
      setDescription(descriptionParam);
    }
  }, [searchParams]);
  const [livePreview, setLivePreview] = useState(true);

  const handleGenerate = async () => {
    if (!description.trim()) {
      alert('Please enter a description');
      return;
    }

    setIsGenerating(true);
    try {
      const response = await fetch('/api/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          description,
          format,
          template: template || undefined,
          options: {
            interactive: false,
            validate: true,
          },
        }),
      });

      if (!response.ok) {
        throw new Error(`Generation failed: ${response.statusText}`);
      }

      const data = await response.json();
      setResult(data.content);

      // Save to gallery
      saveDiagramToGallery(data.content, description);
    } catch (error) {
      console.error('Generation failed:', error);
      alert(error instanceof Error ? error.message : 'Generation failed. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  const saveDiagramToGallery = (content: string, desc: string) => {
    try {
      const savedDiagrams = localStorage.getItem('architek-ai-diagrams');
      const diagrams = savedDiagrams ? JSON.parse(savedDiagrams) : [];

      const newDiagram = {
        id: `diagram-${Date.now()}`,
        name: `Generated Diagram ${diagrams.length + 1}`,
        content,
        description: desc,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        tags: [format, template].filter(Boolean),
      };

      diagrams.unshift(newDiagram);
      localStorage.setItem('architek-ai-diagrams', JSON.stringify(diagrams));
    } catch (error) {
      console.warn('Failed to save diagram to gallery:', error);
    }
  };

  const handleFileUpload = (file: File, content: string) => {
    setResult(content);
    setDescription(`Uploaded from ${file.name}`);
    saveDiagramToGallery(content, `Uploaded from ${file.name}`);
  };

  const handleGallerySelect = (diagram: any) => {
    setResult(diagram.content);
    setDescription(diagram.description || diagram.name);
    setActiveTab('generate');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-blue-900 py-8">
      <div className="container mx-auto px-4">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">
              Architecture Diagram Studio
            </h1>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Create, upload, and manage your architecture diagrams with AI-powered generation
            </p>
          </div>

          {/* Tab Navigation */}
          <div className="mb-8">
            <nav className="flex justify-center">
              <div className="flex space-x-1 bg-white dark:bg-gray-800 p-1 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700">
                {[
                  { id: 'generate', label: 'Generate', icon: '✨' },
                  { id: 'upload', label: 'Upload', icon: '📁' },
                  { id: 'gallery', label: 'Gallery', icon: '🖼️' },
                ].map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`flex items-center px-6 py-3 text-sm font-semibold rounded-lg transition-all duration-300 ${
                      activeTab === tab.id
                        ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg transform scale-105'
                        : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700'
                    }`}
                  >
                    <span className="mr-2 text-lg">{tab.icon}</span>
                    {tab.label}
                  </button>
                ))}
              </div>
            </nav>
          </div>

          {/* Generate Tab */}
          {activeTab === 'generate' && (
            <div className="grid lg:grid-cols-2 gap-8">
            {/* Input Section */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 border border-gray-100 dark:border-gray-700">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
                <span className="mr-3 text-2xl">🎨</span>
                Describe Your Architecture
              </h2>

              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                    Architecture Description
                  </label>
                  <textarea
                    value={description}
                    onChange={(e) => {
                      setDescription(e.target.value);
                      // Live preview for Mermaid content
                      if (livePreview && format === 'mermaid' && e.target.value.trim().startsWith('graph')) {
                        setResult(e.target.value);
                      }
                    }}
                    placeholder="e.g., microservices architecture with API gateway, user service, and payment service"
                    className="w-full h-36 px-4 py-3 border-2 border-gray-200 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200 resize-none"
                  />
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                      Output Format
                    </label>
                    <select
                      value={format}
                      onChange={(e) => setFormat(e.target.value)}
                      className="w-full px-4 py-3 border-2 border-gray-200 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200"
                    >
                      <option value="mermaid">🔷 Mermaid</option>
                      <option value="plantuml">🌱 PlantUML</option>
                      <option value="ascii">📝 ASCII</option>
                      <option value="drawio">🎨 Draw.io</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                      Theme
                    </label>
                    <select
                      value={diagramTheme}
                      onChange={(e) => setDiagramTheme(e.target.value as any)}
                      className="w-full px-4 py-3 border-2 border-gray-200 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200"
                    >
                      <option value="default">☀️ Default</option>
                      <option value="dark">🌙 Dark</option>
                      <option value="forest">🌲 Forest</option>
                      <option value="neutral">⚪ Neutral</option>
                    </select>
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                    Template (Optional)
                  </label>
                  <select
                    value={template}
                    onChange={(e) => setTemplate(e.target.value)}
                    className="w-full px-4 py-3 border-2 border-gray-200 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200"
                  >
                    <option value="">🎯 No Template</option>
                    <option value="microservices">🔧 Microservices</option>
                    <option value="monolithic">🏗️ Monolithic</option>
                    <option value="serverless">☁️ Serverless</option>
                  </select>
                </div>

                <div className="flex items-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-xl border border-blue-200 dark:border-blue-800">
                  <input
                    type="checkbox"
                    id="livePreview"
                    checked={livePreview}
                    onChange={(e) => setLivePreview(e.target.checked)}
                    className="h-5 w-5 text-blue-600 focus:ring-blue-500 border-gray-300 rounded transition-all duration-200"
                  />
                  <label htmlFor="livePreview" className="ml-3 block text-sm font-medium text-blue-700 dark:text-blue-300">
                    ⚡ Enable live preview (for Mermaid syntax)
                  </label>
                </div>

                <button
                  onClick={handleGenerate}
                  disabled={isGenerating || !description.trim()}
                  className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 disabled:from-gray-400 disabled:to-gray-400 text-white py-4 px-6 rounded-xl font-bold text-lg transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 disabled:transform-none disabled:hover:scale-100"
                >
                  {isGenerating ? (
                    <span className="flex items-center justify-center">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Generating Magic...
                    </span>
                  ) : (
                    <span className="flex items-center justify-center">
                      ✨ Generate Diagram
                    </span>
                  )}
                </button>
              </div>
            </div>
            
            {/* Output Section */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 border border-gray-100 dark:border-gray-700">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                  <span className="mr-3 text-2xl">🎯</span>
                  Generated Diagram
                </h2>
                {result && (
                  <ExportButton
                    content={result}
                    filename="architecture-diagram"
                    className="ml-4 scale-110"
                  />
                )}
              </div>

              {result ? (
                <div className="space-y-6">
                  {/* Live Preview */}
                  {format === 'mermaid' && (
                    <div className="border-2 border-blue-200 dark:border-blue-800 rounded-xl p-6 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20">
                      <h3 className="text-lg font-semibold text-blue-700 dark:text-blue-300 mb-4 flex items-center">
                        <span className="mr-2">👁️</span>
                        Live Preview
                      </h3>
                      <div className="bg-white dark:bg-gray-700 rounded-lg border-2 border-gray-200 dark:border-gray-600 overflow-hidden">
                        <MermaidPreview
                          content={result}
                          theme={resolvedTheme === 'dark' ? 'dark' : diagramTheme}
                          className="min-h-[300px]"
                        />
                      </div>
                    </div>
                  )}

                  {/* Code View */}
                  <div>
                    <h3 className="text-sm font-medium text-gray-700 mb-2">Generated Code</h3>
                    <div className="bg-gray-100 p-4 rounded-lg">
                      <pre className="text-sm font-mono whitespace-pre-wrap overflow-x-auto">
                        {result}
                      </pre>
                    </div>
                  </div>

                  <div className="flex space-x-2">
                    <button
                      onClick={() => navigator.clipboard.writeText(result)}
                      className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-semibold transition-colors"
                    >
                      Copy Code
                    </button>
                    <button
                      onClick={() => {
                        const blob = new Blob([result], { type: 'text/plain' });
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = 'architecture.mmd';
                        a.click();
                        URL.revokeObjectURL(url);
                      }}
                      className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-semibold transition-colors"
                    >
                      Download .mmd
                    </button>
                  </div>
                </div>
              ) : (
                <div className="text-center text-gray-500 py-12">
                  <div className="text-4xl mb-4">📊</div>
                  <p>Your generated diagram will appear here</p>
                  <p className="text-sm mt-2">Enter a description and click "Generate Diagram" to get started</p>
                </div>
              )}
            </div>
          </div>
          )}

          {/* Upload Tab */}
          {activeTab === 'upload' && (
            <div className="max-w-4xl mx-auto">
              <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-xl font-semibold mb-4">Upload Mermaid Files</h2>
                <FileUpload
                  onFileSelect={handleFileUpload}
                  onError={(error) => alert(error)}
                />

                {result && (
                  <div className="mt-8">
                    <h3 className="text-lg font-medium mb-4">Uploaded Diagram</h3>
                    <div className="grid lg:grid-cols-2 gap-6">
                      <div>
                        <h4 className="text-sm font-medium text-gray-700 mb-2">Preview</h4>
                        <MermaidPreview
                          content={result}
                          theme={resolvedTheme === 'dark' ? 'dark' : diagramTheme}
                          className="border border-gray-200 rounded-lg p-4"
                        />
                      </div>
                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="text-sm font-medium text-gray-700">Code</h4>
                          <ExportButton
                            content={result}
                            filename="uploaded-diagram"
                          />
                        </div>
                        <div className="bg-gray-100 p-4 rounded-lg">
                          <pre className="text-sm font-mono whitespace-pre-wrap overflow-x-auto">
                            {result}
                          </pre>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Gallery Tab */}
          {activeTab === 'gallery' && (
            <DiagramGallery
              onSelectDiagram={handleGallerySelect}
              onDeleteDiagram={(_id) => {
                // Refresh gallery after deletion
                window.location.reload();
              }}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default GeneratePage;
