import React from 'react';
import { Link } from 'react-router-dom';
import {
  SparklesIcon,
  RocketLaunchIcon,
  CpuChipIcon,
  CloudIcon,
  BoltIcon
} from '@heroicons/react/24/outline';

const HomePage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-purple-900">
      <div className="container mx-auto px-4 py-16">
        {/* Hero Section */}
        <div className="text-center mb-20">
          <div className="flex justify-center mb-6">
            <div className="relative">
              <div className="h-20 w-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-2xl">
                <SparklesIcon className="h-10 w-10 text-white" />
              </div>
              <div className="absolute -top-1 -right-1 h-6 w-6 bg-yellow-400 rounded-full flex items-center justify-center">
                <BoltIcon className="h-4 w-4 text-yellow-800" />
              </div>
            </div>
          </div>

          <h1 className="text-6xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-6">
            ArchitekAI
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-4xl mx-auto leading-relaxed">
            Transform natural language descriptions into professional architecture diagrams.
            Powered by AI, designed for developers and architects who value precision and beauty.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Link
              to="/generate"
              className="group bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 flex items-center"
            >
              <RocketLaunchIcon className="h-5 w-5 mr-2 group-hover:animate-pulse" />
              Generate Diagram
            </Link>
            <Link
              to="/templates"
              className="bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 text-blue-600 dark:text-blue-400 px-8 py-4 rounded-xl font-semibold border-2 border-blue-600 dark:border-blue-400 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105"
            >
              Browse Templates
            </Link>
          </div>
        </div>

        {/* Features Section */}
        <div className="grid md:grid-cols-3 gap-8 mb-20">
          <div className="group bg-white dark:bg-gray-800 p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-105 border border-gray-100 dark:border-gray-700">
            <div className="flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl mb-6 group-hover:scale-110 transition-transform duration-300">
              <CpuChipIcon className="h-8 w-8 text-white" />
            </div>
            <h3 className="text-xl font-bold mb-4 text-gray-900 dark:text-white">AI-Powered Analysis</h3>
            <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
              Advanced NLP engine understands your architecture descriptions and
              automatically identifies components and relationships with precision.
            </p>
          </div>

          <div className="group bg-white dark:bg-gray-800 p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-105 border border-gray-100 dark:border-gray-700">
            <div className="flex items-center justify-center w-16 h-16 bg-gradient-to-br from-green-500 to-blue-600 rounded-2xl mb-6 group-hover:scale-110 transition-transform duration-300">
              <CloudIcon className="h-8 w-8 text-white" />
            </div>
            <h3 className="text-xl font-bold mb-4 text-gray-900 dark:text-white">Multiple Formats</h3>
            <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
              Generate diagrams in Mermaid, PlantUML, ASCII, Draw.io, and more.
              Choose the format that works best for your workflow and tools.
            </p>
          </div>

          <div className="group bg-white dark:bg-gray-800 p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-105 border border-gray-100 dark:border-gray-700">
            <div className="flex items-center justify-center w-16 h-16 bg-gradient-to-br from-yellow-500 to-red-600 rounded-2xl mb-6 group-hover:scale-110 transition-transform duration-300">
              <BoltIcon className="h-8 w-8 text-white" />
            </div>
            <h3 className="text-xl font-bold mb-4 text-gray-900 dark:text-white">Lightning Fast</h3>
            <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
              Generate professional architecture diagrams in seconds, not hours.
              Perfect for rapid prototyping and comprehensive documentation.
            </p>
          </div>
        </div>

        {/* Example Section */}
        <div className="bg-white rounded-lg shadow-md p-8">
          <h2 className="text-2xl font-bold text-center mb-8">See It In Action</h2>
          <div className="grid md:grid-cols-2 gap-8 items-center">
            <div>
              <h3 className="text-lg font-semibold mb-4">Input Description:</h3>
              <div className="bg-gray-100 p-4 rounded-lg font-mono text-sm">
                "microservices architecture with API gateway, user service, and payment service"
              </div>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-4">Generated Diagram:</h3>
              <div className="bg-gray-100 p-4 rounded-lg">
                <div className="text-xs text-gray-600 mb-2">Mermaid Diagram Preview</div>
                <div className="bg-white p-3 rounded border text-xs font-mono">
                  graph TD<br/>
                  &nbsp;&nbsp;A[API Gateway] --&gt; B[User Service]<br/>
                  &nbsp;&nbsp;A --&gt; C[Payment Service]<br/>
                  &nbsp;&nbsp;B --&gt; D[(User DB)]<br/>
                  &nbsp;&nbsp;C --&gt; E[(Payment DB)]
                </div>
              </div>
            </div>
          </div>
          <div className="text-center mt-8">
            <Link
              to="/generate"
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-semibold transition-colors"
            >
              Try It Now
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HomePage;
