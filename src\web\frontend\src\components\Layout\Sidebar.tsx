import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Transition } from '@headlessui/react';
import { XMarkIcon } from '@heroicons/react/24/outline';

interface SidebarProps {
  open?: boolean;
  onClose?: () => void;
  mobile?: boolean;
}

const Sidebar: React.FC<SidebarProps> = ({ open = true, onClose, mobile = false }) => {
  const location = useLocation();

  const navigation = [
    { name: 'Home', href: '/', icon: '🏠', description: 'Welcome & overview' },
    { name: 'Generate', href: '/generate', icon: '⚡', description: 'Create new diagrams' },
    { name: 'Templates', href: '/templates', icon: '📋', description: 'Architecture templates' },
    { name: 'Examples', href: '/examples', icon: '📊', description: 'Sample diagrams' },
    { name: 'Documentation', href: '/docs', icon: '📚', description: 'Guides & references' },
    { name: 'Settings', href: '/settings', icon: '⚙️', description: 'App preferences' },
  ];

  return (
    <>
      {/* Mobile backdrop */}
      <Transition
        show={mobile && open}
        as={React.Fragment}
        enter="transition-opacity ease-linear duration-300"
        enterFrom="opacity-0"
        enterTo="opacity-100"
        leave="transition-opacity ease-linear duration-300"
        leaveFrom="opacity-100"
        leaveTo="opacity-0"
      >
        <div className="fixed inset-0 z-40 bg-gray-600 bg-opacity-75" onClick={onClose} />
      </Transition>

      {/* Mobile sidebar */}
      <Transition
        show={mobile && open}
        as={React.Fragment}
        enter="transition ease-in-out duration-300 transform"
        enterFrom="-translate-x-full"
        enterTo="translate-x-0"
        leave="transition ease-in-out duration-300 transform"
        leaveFrom="translate-x-0"
        leaveTo="-translate-x-full"
      >
        <div className="fixed inset-0 z-50 lg:hidden">
          <div className="flex h-full w-64 flex-col bg-white dark:bg-gray-800 shadow-xl border-r border-gray-200 dark:border-gray-700">
            {/* Mobile header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="h-8 w-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                    <span className="text-white font-bold text-sm">AI</span>
                  </div>
                </div>
                <h2 className="ml-3 text-xl font-bold text-gray-900 dark:text-white">ArchitekAI</h2>
              </div>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 p-2 rounded-lg transition-all duration-200"
                aria-label="Close sidebar"
              >
                <XMarkIcon className="h-5 w-5" />
              </button>
            </div>
            <SidebarContent navigation={navigation} currentPath={location.pathname} onItemClick={onClose} />
          </div>
        </div>
      </Transition>

      {/* Desktop sidebar */}
      {!mobile && (
        <div className="flex h-full flex-col bg-white dark:bg-gray-800 shadow-sm border-r border-gray-200 dark:border-gray-700">
          {/* Desktop header */}
          <div className="flex items-center p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="h-8 w-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">AI</span>
                </div>
              </div>
              <h2 className="ml-3 text-xl font-bold text-gray-900 dark:text-white">ArchitekAI</h2>
            </div>
          </div>
          <SidebarContent navigation={navigation} currentPath={location.pathname} />
        </div>
      )}
    </>
  );
};

interface SidebarContentProps {
  navigation: Array<{ name: string; href: string; icon: string; description: string }>;
  currentPath: string;
  onItemClick?: () => void | undefined;
}

const SidebarContent: React.FC<SidebarContentProps> = ({ navigation, currentPath, onItemClick }) => {
  return (
    <nav className="flex-1 p-4 space-y-1 overflow-y-auto">
      {navigation.map((item) => {
        const isActive = currentPath === item.href;
        return (
          <Link
            key={item.name}
            to={item.href}
            onClick={onItemClick}
            className={`group flex items-center px-3 py-3 rounded-lg text-sm font-medium transition-all duration-200 ${
              isActive
                ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg transform scale-[1.02]'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-700 hover:shadow-md hover:transform hover:scale-[1.01]'
            }`}
            title={item.description}
          >
            <span className={`mr-3 text-lg transition-transform duration-200 ${
              isActive ? 'scale-110' : 'group-hover:scale-110'
            }`}>
              {item.icon}
            </span>
            <div className="flex flex-col">
              <span className="font-semibold">{item.name}</span>
              <span className={`text-xs ${
                isActive
                  ? 'text-blue-100'
                  : 'text-gray-400 dark:text-gray-500 group-hover:text-gray-500 dark:group-hover:text-gray-400'
              }`}>
                {item.description}
              </span>
            </div>
          </Link>
        );
      })}

      {/* Footer section */}
      <div className="mt-8 pt-4 border-t border-gray-200 dark:border-gray-700">
        <div className="px-3 py-2">
          <p className="text-xs text-gray-500 dark:text-gray-400">
            Version 1.0.0
          </p>
          <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
            Built with ❤️ by ArchitekAI
          </p>
        </div>
      </div>
    </nav>
  );
};

export default Sidebar;
