import React, { useState } from 'react';
import { ArrowDownTrayIcon, Cog6ToothIcon } from '@heroicons/react/24/outline';
import { Menu, Transition } from '@headlessui/react';
import { Fragment } from 'react';

interface ExportButtonProps {
  content: string;
  filename?: string;
  onExport?: (format: string, options: ExportOptions) => void;
  disabled?: boolean;
  className?: string;
}

interface ExportOptions {
  format: 'jpg' | 'png' | 'svg';
  quality?: number;
  width?: number;
  height?: number;
  theme?: 'default' | 'dark' | 'forest' | 'neutral';
  backgroundColor?: string;
}

const ExportButton: React.FC<ExportButtonProps> = ({
  content,
  filename = 'diagram',
  onExport,
  disabled = false,
  className = '',
}) => {
  const [isExporting, setIsExporting] = useState(false);
  const [showOptions, setShowOptions] = useState(false);
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'jpg',
    quality: 90,
    width: 1920,
    height: 1080,
    theme: 'default',
    backgroundColor: '#ffffff',
  });

  const handleExport = async (format: 'jpg' | 'png' | 'svg', customOptions?: Partial<ExportOptions>) => {
    if (!content.trim() || disabled) return;

    setIsExporting(true);
    
    try {
      const options = { ...exportOptions, format, ...customOptions };
      
      const response = await fetch('/api/convert', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content,
          format,
          options,
        }),
      });

      if (!response.ok) {
        throw new Error(`Export failed: ${response.statusText}`);
      }

      // Download the file
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${filename}.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      if (onExport) {
        onExport(format, options);
      }
    } catch (error) {
      console.error('Export failed:', error);
      alert(error instanceof Error ? error.message : 'Export failed');
    } finally {
      setIsExporting(false);
    }
  };

  const quickExportFormats = [
    { format: 'jpg' as const, label: 'JPEG', icon: '🖼️' },
    { format: 'png' as const, label: 'PNG', icon: '🖼️' },
    { format: 'svg' as const, label: 'SVG', icon: '📐' },
  ];

  return (
    <div className={`export-button ${className}`}>
      <div className="flex items-center space-x-2">
        {/* Quick Export Buttons */}
        <div className="flex space-x-1">
          {quickExportFormats.map(({ format, label, icon }) => (
            <button
              key={format}
              onClick={() => handleExport(format)}
              disabled={disabled || isExporting || !content.trim()}
              className={`group inline-flex items-center px-4 py-2 text-sm font-semibold rounded-lg transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105 disabled:transform-none disabled:opacity-50 disabled:cursor-not-allowed ${
                format === 'jpg' ? 'bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white' :
                format === 'png' ? 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white' :
                'bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white'
              }`}
              title={`Export as ${label}`}
            >
              <span className="mr-2 group-hover:animate-bounce">{icon}</span>
              {label}
            </button>
          ))}
        </div>

        {/* Advanced Options Menu */}
        <Menu as="div" className="relative inline-block text-left">
          <div>
            <Menu.Button
              disabled={disabled || isExporting || !content.trim()}
              className="group inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border-2 border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105 disabled:transform-none"
            >
              <Cog6ToothIcon className="h-4 w-4 mr-2 group-hover:animate-spin" />
              Options
            </Menu.Button>
          </div>

          <Transition
            as={Fragment}
            enter="transition ease-out duration-100"
            enterFrom="transform opacity-0 scale-95"
            enterTo="transform opacity-100 scale-100"
            leave="transition ease-in duration-75"
            leaveFrom="transform opacity-100 scale-100"
            leaveTo="transform opacity-0 scale-95"
          >
            <Menu.Panel className="absolute right-0 z-10 mt-2 w-80 origin-top-right bg-white border border-gray-200 rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
              <div className="p-4">
                <h3 className="text-sm font-medium text-gray-900 mb-3">Export Options</h3>
                
                <div className="space-y-3">
                  {/* Format Selection */}
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      Format
                    </label>
                    <select
                      value={exportOptions.format}
                      onChange={(e) => setExportOptions(prev => ({ 
                        ...prev, 
                        format: e.target.value as 'jpg' | 'png' | 'svg' 
                      }))}
                      className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                    >
                      <option value="jpg">JPEG</option>
                      <option value="png">PNG</option>
                      <option value="svg">SVG</option>
                    </select>
                  </div>

                  {/* Quality (for JPEG) */}
                  {exportOptions.format === 'jpg' && (
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Quality ({exportOptions.quality}%)
                      </label>
                      <input
                        type="range"
                        min="10"
                        max="100"
                        value={exportOptions.quality}
                        onChange={(e) => setExportOptions(prev => ({ 
                          ...prev, 
                          quality: parseInt(e.target.value) 
                        }))}
                        className="w-full"
                      />
                    </div>
                  )}

                  {/* Dimensions */}
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Width (px)
                      </label>
                      <input
                        type="number"
                        value={exportOptions.width}
                        onChange={(e) => setExportOptions(prev => ({ 
                          ...prev, 
                          width: parseInt(e.target.value) || 1920 
                        }))}
                        className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Height (px)
                      </label>
                      <input
                        type="number"
                        value={exportOptions.height}
                        onChange={(e) => setExportOptions(prev => ({ 
                          ...prev, 
                          height: parseInt(e.target.value) || 1080 
                        }))}
                        className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                      />
                    </div>
                  </div>

                  {/* Theme */}
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      Theme
                    </label>
                    <select
                      value={exportOptions.theme}
                      onChange={(e) => setExportOptions(prev => ({ 
                        ...prev, 
                        theme: e.target.value as 'default' | 'dark' | 'forest' | 'neutral' 
                      }))}
                      className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                    >
                      <option value="default">Default</option>
                      <option value="dark">Dark</option>
                      <option value="forest">Forest</option>
                      <option value="neutral">Neutral</option>
                    </select>
                  </div>

                  {/* Background Color */}
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      Background Color
                    </label>
                    <input
                      type="color"
                      value={exportOptions.backgroundColor}
                      onChange={(e) => setExportOptions(prev => ({ 
                        ...prev, 
                        backgroundColor: e.target.value 
                      }))}
                      className="w-full h-8 border border-gray-300 rounded cursor-pointer"
                    />
                  </div>

                  {/* Export Button */}
                  <button
                    onClick={() => handleExport(exportOptions.format, exportOptions)}
                    disabled={isExporting}
                    className="w-full mt-4 inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isExporting ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Exporting...
                      </>
                    ) : (
                      <>
                        <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                        Export {exportOptions.format.toUpperCase()}
                      </>
                    )}
                  </button>
                </div>
              </div>
            </Menu.Panel>
          </Transition>
        </Menu>
      </div>

      {isExporting && (
        <div className="mt-2 text-sm text-gray-600">
          Generating {exportOptions.format.toUpperCase()} export...
        </div>
      )}
    </div>
  );
};

export default ExportButton;
