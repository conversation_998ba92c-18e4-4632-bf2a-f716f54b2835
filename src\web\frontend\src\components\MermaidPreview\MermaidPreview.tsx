import React, { useEffect, useRef, useState } from 'react';
import mermaid from 'mermaid';

interface MermaidPreviewProps {
  content: string;
  theme?: 'default' | 'dark' | 'forest' | 'neutral';
  className?: string;
  onError?: (error: string) => void;
  onRender?: (svg: string) => void;
}

const MermaidPreview: React.FC<MermaidPreviewProps> = ({
  content,
  theme = 'default',
  className = '',
  onError,
  onRender,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Initialize Mermaid
    mermaid.initialize({
      startOnLoad: false,
      theme,
      themeVariables: {
        primaryColor: '#ff6b6b',
        primaryTextColor: '#333',
        primaryBorderColor: '#ff6b6b',
        lineColor: '#333',
        secondaryColor: '#4ecdc4',
        tertiaryColor: '#ffe66d',
      },
      securityLevel: 'loose',
    });
  }, [theme]);

  useEffect(() => {
    if (!content.trim() || !containerRef.current) {
      return;
    }

    const renderDiagram = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Clear previous content
        if (containerRef.current) {
          containerRef.current.innerHTML = '';
        }

        // Generate unique ID for this diagram
        const diagramId = `mermaid-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

        // Render the diagram
        const { svg } = await mermaid.render(diagramId, content);

        if (containerRef.current) {
          containerRef.current.innerHTML = svg;
          
          // Call onRender callback with the SVG content
          if (onRender) {
            onRender(svg);
          }
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to render diagram';
        setError(errorMessage);
        
        if (onError) {
          onError(errorMessage);
        }
        
        if (containerRef.current) {
          containerRef.current.innerHTML = `
            <div class="flex items-center justify-center h-32 bg-red-50 border border-red-200 rounded-lg">
              <div class="text-center">
                <div class="text-red-600 text-sm font-medium">Diagram Error</div>
                <div class="text-red-500 text-xs mt-1">${errorMessage}</div>
              </div>
            </div>
          `;
        }
      } finally {
        setIsLoading(false);
      }
    };

    renderDiagram();
  }, [content, theme, onError, onRender]);

  return (
    <div className={`mermaid-preview ${className}`}>
      {isLoading && (
        <div className="flex items-center justify-center h-40 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border-2 border-blue-200 dark:border-blue-800 rounded-xl">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-4 border-blue-200 border-t-blue-600 mx-auto"></div>
            <div className="text-blue-600 dark:text-blue-400 text-lg font-semibold mt-4">Rendering diagram...</div>
            <div className="text-gray-500 dark:text-gray-400 text-sm mt-1">Creating your beautiful visualization</div>
          </div>
        </div>
      )}
      
      <div
        ref={containerRef}
        className={`mermaid-container ${isLoading ? 'hidden' : ''}`}
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '200px',
        }}
      />
      
      {error && !isLoading && (
        <div className="mt-4 p-6 bg-gradient-to-br from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 border-2 border-red-200 dark:border-red-800 rounded-xl">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <div className="h-8 w-8 bg-red-100 dark:bg-red-900/50 rounded-full flex items-center justify-center">
                <svg className="h-5 w-5 text-red-600 dark:text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
            </div>
            <div className="ml-3">
              <div className="text-red-800 dark:text-red-300 text-lg font-semibold">Rendering Error</div>
              <div className="text-red-600 dark:text-red-400 text-sm mt-1 font-medium">{error}</div>
              <div className="text-red-500 dark:text-red-500 text-xs mt-2">Please check your diagram syntax and try again.</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MermaidPreview;
